<p style="display:flex; justify-content: center">

</p>
<p align="center">
  <a href="https://tdesign.tencent.com/vue-next/overview" target="_blank">
   <img alt="TDesign Logo" width="200" src="./public/tdesign-logo.svg"/>
  </a>
</p>

<p align="center">
  <a href="https://nodejs.org/en/about/releases/"><img src="https://img.shields.io/node/v/vite.svg" alt="node compatility"></a>
  <a href="https://github.com/Tencent/tdesign-react-starter/blob/develop/LICENSE">
    <img src="https://img.shields.io/npm/l/tdesign-react.svg?sanitize=true" alt="License">
  </a>
</p>

## 项目简介

`tdesign-vue-next` 是一个 TDesign 适配桌面端的组件库，适合在 vue3.x 技术栈项目中使用。

## 开发

### 安装依赖

```bash
npm install
```

### 启动项目

```bash
npm run dev
```

## 构建

### 构建正式环境

```bash
npm run build
```

## 开源协议

TDesign 遵循 [MIT 协议](https://github.com/Tencent/tdesign-starter-cli/blob/develop/LICENSE)。
