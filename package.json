{"name": "zgzn", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 8000", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "prepare": "node -e \"if(require('fs').existsSync('.git')){process.exit(1)}\" || is-ci || husky install"}, "dependencies": {"@vueuse/core": "^13.6.0", "frontend-utils": "^0.2.18", "lodash": "^4.17.21", "tdesign-icons-vue-next": "latest", "tdesign-vue-next": "latest", "vite-svg-loader": "^5.1.0", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "eslint": "^9.32.0", "eslint-plugin-vue": "^10.3.0", "husky": "^9.1.7", "is-ci": "^4.1.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "typescript": "^5.8.3", "vite": "^7.0.6", "vue-tsc": "^3.0.4"}, "description": "Base on tdesign-starter-cli"}